"""
用户蓝图 - 简化版
包含所有用户前台相关的路由
"""
import requests
import markdown
from datetime import timedelta
from flask import Blueprint, request, jsonify, render_template
from models import db, Card, Project, Group, Number, UserNumber, CardNumberBinding, get_beijing_time
import config

user_bp = Blueprint('user', __name__)


def validate_card_code(card_code):
    """验证卡密格式"""
    if not card_code or len(card_code) < 8:
        return False
    return True


def get_dynamic_ui_config():
    """获取动态UI配置"""
    # 根据轮询配置计算倒计时时间
    sms_wait_countdown = int(config.MAX_POLLING_ATTEMPTS * config.POLL_INTERVAL_MS / 1000)

    dynamic_config = {
        'REPLACE_NUMBER_COOLDOWN': config.UI_CONFIG['REPLACE_NUMBER_COOLDOWN'],
        'SMS_WAIT_COUNTDOWN': sms_wait_countdown,
        'MAX_POLLING_ATTEMPTS': config.MAX_POLLING_ATTEMPTS,
        'POLL_INTERVAL_MS': config.POLL_INTERVAL_MS,
        'WAITING_SMS_MESSAGE': config.UI_CONFIG['WAITING_SMS_MESSAGE'],
        'STATUS_DISPLAY_TIME': config.UI_CONFIG['STATUS_DISPLAY_TIME'],
        'SHOW_WAIT_LOG_BUTTON': config.UI_CONFIG['SHOW_WAIT_LOG_BUTTON'],
    }
    return dynamic_config


@user_bp.route('/')
def root():
    """根路径处理"""
    card_key = request.args.get('key', '').strip()
    
    if not card_key:
        return render_template('user.html', 
                             error='请提供有效的卡密参数',
                             project_name='SMS验证码系统')
    
    return sms_token_entry()


def sms_token_entry():
    """SMS令牌入口页面"""
    try:
        card_key = request.args.get('key', '').strip()
        
        if not validate_card_code(card_key):
            return render_template('user.html', 
                                 error='卡密格式无效',
                                 project_name='SMS验证码系统')
        
        # 查找卡密
        card = db.session.execute(
            db.select(Card).filter_by(code=card_key)
        ).scalars().first()
        
        if not card:
            return render_template('user.html', 
                                 error='卡密不存在或已失效',
                                 project_name='SMS验证码系统')
        
        if card.is_used:
            return render_template('user.html', 
                                 error='卡密已被使用',
                                 project_name='SMS验证码系统')
        
        # 获取项目信息
        project = card.project
        if not project:
            return render_template('user.html', 
                                 error='项目不存在',
                                 project_name='SMS验证码系统')
        
        # 处理项目描述的Markdown
        project_description_html = ''
        if project.description:
            project_description_html = markdown.markdown(project.description)
        
        return render_template('user.html',
                             project_name=project.name,
                             project_description=project_description_html,
                             card_key=card_key,
                             ui_config=get_dynamic_ui_config(),
                             server_config={
                                 'HOST': config.SERVER_CONFIG['HOST'],
                                 'PORT': config.SERVER_CONFIG['PORT']
                             })
        
    except Exception as e:
        return render_template('user.html', 
                             error='服务器内部错误',
                             project_name='SMS验证码系统')


@user_bp.route('/get_number', methods=['POST'])
def get_number():
    """获取号码"""
    try:
        data = request.get_json()
        card_key = data.get('key', '').strip()
        
        if not validate_card_code(card_key):
            return jsonify({'success': False, 'error': '卡密格式无效'})
        
        # 查找卡密
        card = db.session.execute(
            db.select(Card).filter_by(code=card_key)
        ).scalars().first()
        
        if not card:
            return jsonify({'success': False, 'error': '卡密不存在或已失效'})
        
        if card.is_used:
            return jsonify({'success': False, 'error': '卡密已被使用'})
        
        # 获取项目的号码组
        groups = db.session.execute(
            db.select(Group).filter_by(project_id=card.project_id, is_disabled=False)
        ).scalars().all()
        
        if not groups:
            return jsonify({'success': False, 'error': '暂无可用号码组'})
        
        # 从第一个号码组获取可用号码
        group = groups[0]
        available_number = db.session.execute(
            db.select(Number).filter_by(
                group_id=group.id,
                status='available',
                is_disabled=False
            ).limit(1)
        ).scalars().first()
        
        if not available_number:
            return jsonify({'success': False, 'error': '暂无可用号码'})
        
        # 更新号码状态
        available_number.status = 'in_use'
        available_number.last_use_time = get_beijing_time()
        
        # 创建用户号码记录
        user_number = UserNumber(
            card_code=card_key,
            number_id=available_number.id,
            get_time=get_beijing_time()
        )
        db.session.add(user_number)
        
        # 创建卡密号码绑定
        binding = CardNumberBinding(
            card_code=card_key,
            number_id=available_number.id,
            project_id=card.project_id,
            created_at=get_beijing_time()
        )
        db.session.add(binding)
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'number': available_number.number,
            'com': available_number.com
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': '服务器内部错误'})


@user_bp.route('/get_sms', methods=['POST'])
def get_sms():
    """获取短信验证码"""
    try:
        data = request.get_json()
        card_key = data.get('key', '').strip()
        
        if not validate_card_code(card_key):
            return jsonify({'success': False, 'error': '卡密格式无效'})
        
        # 查找用户号码记录
        user_number = db.session.execute(
            db.select(UserNumber).filter_by(card_code=card_key)
            .order_by(UserNumber.get_time.desc())
        ).scalars().first()
        
        if not user_number:
            return jsonify({'success': False, 'error': '请先获取号码'})
        
        number = user_number.number
        if not number:
            return jsonify({'success': False, 'error': '号码不存在'})
        
        # 调用SMS API获取验证码
        try:
            api_url = f"{config.SMS_API_HOST}/api/get_message"
            response = requests.get(api_url, params={
                'token': number.group.token,
                'number': number.number
            }, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    sms_data = result.get('data', {})
                    sms_code = sms_data.get('code', '')
                    sms_content = sms_data.get('content', '')
                    
                    if sms_code:
                        # 更新卡密记录
                        card = db.session.execute(
                            db.select(Card).filter_by(code=card_key)
                        ).scalars().first()
                        
                        if card:
                            card.sms_code = sms_code
                            card.sms_content = sms_content
                            card.use_time = get_beijing_time()
                            card.is_used = True
                            card.number = number.number
                            card.com = number.com
                            
                            db.session.commit()
                        
                        return jsonify({
                            'success': True,
                            'code': sms_code,
                            'content': sms_content
                        })
                    else:
                        return jsonify({'success': False, 'error': '暂无短信'})
                else:
                    return jsonify({'success': False, 'error': result.get('error', 'API调用失败')})
            else:
                return jsonify({'success': False, 'error': 'SMS服务暂时不可用'})
                
        except requests.RequestException:
            return jsonify({'success': False, 'error': 'SMS服务连接失败'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': '服务器内部错误'})


@user_bp.route('/replace_number', methods=['POST'])
def replace_number():
    """更换号码"""
    try:
        data = request.get_json()
        card_key = data.get('key', '').strip()
        
        if not validate_card_code(card_key):
            return jsonify({'success': False, 'error': '卡密格式无效'})
        
        # 查找卡密
        card = db.session.execute(
            db.select(Card).filter_by(code=card_key)
        ).scalars().first()
        
        if not card:
            return jsonify({'success': False, 'error': '卡密不存在或已失效'})
        
        if card.is_used:
            return jsonify({'success': False, 'error': '卡密已被使用'})
        
        # 释放当前绑定的号码
        current_binding = db.session.execute(
            db.select(CardNumberBinding).filter_by(card_code=card_key)
        ).scalars().first()
        
        if current_binding:
            # 更新号码状态为可用
            current_number = current_binding.number
            if current_number:
                current_number.status = 'available'
                current_number.last_use_time = None
            
            # 删除绑定记录
            db.session.delete(current_binding)
        
        # 删除用户号码记录
        user_numbers = db.session.execute(
            db.select(UserNumber).filter_by(card_code=card_key)
        ).scalars().all()
        
        for user_number in user_numbers:
            db.session.delete(user_number)
        
        db.session.commit()
        
        return jsonify({'success': True, 'message': '号码已释放，请重新获取'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': '服务器内部错误'})
