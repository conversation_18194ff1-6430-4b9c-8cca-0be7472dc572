"""
SMS验证码管理系统主应用文件
"""
from flask import Flask, request, jsonify, render_template
from flask_cors import CORS
import os
import secrets
from datetime import timedelta

# 导入模型和配置
from models import db, Admin
import config


def get_or_create_secret_key(instance_dir):
    """获取或创建SECRET_KEY"""
    secret_file = os.path.join(instance_dir, 'secret_key.txt')
    
    if os.path.exists(secret_file):
        with open(secret_file, 'r') as f:
            return f.read().strip()
    else:
        secret_key = secrets.token_hex(32)
        with open(secret_file, 'w') as f:
            f.write(secret_key)
        return secret_key


def create_app():
    """应用工厂函数"""
    # 创建Flask应用
    app = Flask(__name__, static_folder='static', template_folder='templates')

    # 基础配置
    app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 0
    app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(days=31)
    
    # 数据库配置
    BASE_DIR = os.path.abspath(os.path.dirname(__file__))
    INSTANCE_DIR = os.path.join(BASE_DIR, 'instance')
    os.makedirs(INSTANCE_DIR, exist_ok=True)
    
    DB_PATH = os.path.join(INSTANCE_DIR, 'sms.db')
    app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{DB_PATH}'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

    # 设置SECRET_KEY
    secret_key = get_or_create_secret_key(INSTANCE_DIR)
    app.config['SECRET_KEY'] = secret_key

    return app


# 创建应用实例
app = create_app()

# 初始化扩展
CORS(app)
db.init_app(app)

# 导入并注册蓝图
from blueprints.admin import admin_bp
from blueprints.user import user_bp
from blueprints.api import api_bp

app.register_blueprint(admin_bp)
app.register_blueprint(user_bp)
app.register_blueprint(api_bp)


# --- 错误处理 ---
@app.errorhandler(404)
def not_found(e):
    """404错误处理"""
    if request.path.startswith('/api/'):
        return jsonify({'success': False, 'error': '接口不存在'}), 404
    return render_template('user.html', error='页面不存在'), 404


@app.errorhandler(500)
def internal_error(e):
    """500错误处理"""
    db.session.rollback()
    if request.path.startswith('/api/'):
        return jsonify({'success': False, 'error': '服务器内部错误'}), 500
    return render_template('user.html', error='服务器内部错误'), 500


# --- 健康检查 ---
@app.route('/health')
def health_check():
    """健康检查端点"""
    try:
        # 检查数据库连接
        db.session.execute(db.text('SELECT 1'))
        return jsonify({
            'status': 'healthy',
            'database': 'connected'
        })
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'database': 'disconnected',
            'error': str(e)
        }), 500


# --- 数据库初始化 ---
def init_database():
    """初始化数据库"""
    with app.app_context():
        try:
            # 创建所有表
            db.create_all()
            
            # 检查是否存在默认管理员
            admin = db.session.execute(
                db.select(Admin).filter_by(username='admin')
            ).scalars().first()
            
            if not admin:
                # 创建默认管理员账户
                admin = Admin(username='admin')
                admin.set_password(config.ADMIN_DEFAULT_PASSWORD)
                db.session.add(admin)
                db.session.commit()
                print(f"创建默认管理员账户: admin / {config.ADMIN_DEFAULT_PASSWORD}")

            print("数据库初始化完成")
            
        except Exception as e:
            print(f"数据库初始化失败: {str(e)}")
            raise


if __name__ == '__main__':
    # 初始化数据库
    init_database()
    
    # 启动应用
    server_config = config.SERVER_CONFIG
    print(f"启动SMS验证码管理系统...")
    print(f"访问地址: http://{server_config['HOST']}:{server_config['PORT']}")
    print(f"管理后台: http://{server_config['HOST']}:{server_config['PORT']}/admin")
    
    app.run(
        host=server_config['HOST'],
        port=server_config['PORT'],
        debug=server_config['DEBUG'],
        threaded=server_config['THREADED']
    )
