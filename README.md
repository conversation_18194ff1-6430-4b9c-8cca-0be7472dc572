# SMS验证码管理系统

一个基于Flask的SMS验证码管理系统，支持多项目管理、卡密验证、号码分配等功能。

## 功能特性

- 🔐 **安全认证**: 管理员登录、CSRF保护、频率限制
- 📱 **号码管理**: 支持多运营商号码、号码组管理、自动分配
- 🎫 **卡密系统**: 卡密生成、验证、使用记录
- 📊 **统计分析**: 实时统计、数据可视化、使用记录
- 🚀 **高性能**: 缓存优化、数据库索引、连接池
- 🛡️ **安全防护**: XSS防护、SQL注入防护、安全头部
- 📝 **日志记录**: 结构化日志、轮转日志、错误追踪
- 🔧 **易于部署**: Docker支持、Nginx配置、环境变量管理

## 项目结构

```
SFsms-system-v2/
├── app_config.py          # 应用配置管理
├── logger_config.py       # 日志配置
├── exceptions.py          # 自定义异常
├── database_migration.py  # 数据库迁移
├── start.py              # 启动脚本
├── run.py                # 主应用文件
├── models.py             # 数据库模型
├── services.py           # 业务服务层
├── utils.py              # 工具函数
├── security.py           # 安全模块
├── config.py             # 旧配置文件（兼容性）
├── requirements.txt      # Python依赖
├── .env.example         # 环境变量示例
├── blueprints/          # 蓝图模块
│   ├── admin.py         # 管理员路由
│   ├── user.py          # 用户路由
│   └── api.py           # API路由
├── static/              # 静态文件
│   ├── css/
│   └── js/
├── templates/           # 模板文件
├── tests/               # 测试文件
├── deploy/              # 部署配置
│   ├── gunicorn.conf.py # Gunicorn配置
│   └── nginx.conf       # Nginx配置
├── logs/                # 日志目录
└── instance/            # 实例数据
```

## 快速开始

### 1. 环境要求

- Python 3.8+
- SQLite/MySQL/PostgreSQL

### 2. 安装和初始化

```bash
# 克隆项目
git clone <repository-url>
cd SFsms-system-v2

# 初始化项目（自动安装依赖和创建配置文件）
python start.py init

# 修改配置文件
cp .env.example .env
# 编辑 .env 文件，设置数据库连接、API密钥等
```

### 3. 运行开发服务器

```bash
# 开发模式
python start.py dev

# 或直接运行
python run.py
```

### 4. 访问系统

- 用户前台: http://localhost:5600/
- 管理后台: http://localhost:5600/admin
- 默认管理员账号: admin / admin123

## 配置说明

### 环境变量配置

主要配置项（详见 `.env.example`）：

```bash
# 服务器配置
HOST=0.0.0.0
PORT=5600
DEBUG=true

# 数据库配置
DATABASE_URL=sqlite:///instance/sms.db

# 安全配置
SECRET_KEY=your-secret-key
ADMIN_DEFAULT_PASSWORD=admin123

# SMS API配置
SMS_API_HOST=http://api.binstd.com
SMS_API_TOKEN=your_api_token

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
```

### 数据库配置

支持多种数据库：

```bash
# SQLite（默认）
DATABASE_URL=sqlite:///instance/sms.db

# MySQL
DATABASE_URL=mysql+pymysql://user:password@localhost/sms_db

# PostgreSQL
DATABASE_URL=postgresql://user:password@localhost/sms_db
```

## 部署

### 生产环境部署

```bash
# 1. 设置生产环境变量
export FLASK_ENV=production

# 2. 安装生产依赖
pip install gunicorn

# 3. 启动生产服务器
python start.py prod

# 或使用Gunicorn直接启动
gunicorn --config deploy/gunicorn.conf.py run:app
```

### Nginx反向代理

使用提供的Nginx配置文件：

```bash
# 复制配置文件
cp deploy/nginx.conf /etc/nginx/sites-available/sms-system
ln -s /etc/nginx/sites-available/sms-system /etc/nginx/sites-enabled/

# 修改配置文件中的域名和路径
# 重启Nginx
sudo systemctl restart nginx
```

## 开发

### 运行测试

```bash
# 运行所有测试
python start.py test

# 或使用pytest直接运行
pytest tests/ -v
```

### 代码质量检查

```bash
# 代码格式化
black .

# 代码检查
flake8 .
```

### 数据库迁移

```bash
# 运行迁移
python database_migration.py

# 或在应用启动时自动运行
```

## API文档

### 管理员API

- `POST /admin/login` - 管理员登录
- `GET /admin/api/admin/dashboard/stats` - 获取统计数据
- `POST /admin/api/admin/projects` - 创建项目
- `GET /admin/api/admin/projects` - 获取项目列表

### 用户API

- `GET /?key=<card_code>` - 卡密验证和号码获取
- `POST /api/get_number` - 获取手机号码
- `POST /api/get_sms` - 获取验证码
- `POST /api/replace_number` - 更换号码

## 安全特性

- CSRF保护
- XSS防护
- SQL注入防护
- 频率限制
- 安全头部
- 输入验证和清理
- 会话管理

## 监控和日志

- 结构化日志记录
- 轮转日志文件
- 错误追踪
- 性能监控
- API请求日志

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查DATABASE_URL配置
   - 确保数据库服务运行正常

2. **静态文件无法加载**
   - 检查static目录权限
   - 确保Nginx配置正确

3. **SMS API调用失败**
   - 检查SMS_API_HOST和SMS_API_TOKEN配置
   - 查看日志文件获取详细错误信息

### 日志查看

```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log

# 查看访问日志
tail -f logs/access.log
```

## 贡献

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

[MIT License](LICENSE)

## 支持

如有问题，请提交Issue或联系开发团队。
