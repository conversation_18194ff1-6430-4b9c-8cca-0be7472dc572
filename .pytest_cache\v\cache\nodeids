["tests/test_admin.py::TestAdminLogin::test_admin_login_page_loads", "tests/test_basic.py::TestBasicFunctionality::test_admin_login_page", "tests/test_basic.py::TestBasicFunctionality::test_api_get_number_empty_card", "tests/test_basic.py::TestBasicFunctionality::test_api_get_number_invalid_card", "tests/test_basic.py::TestBasicFunctionality::test_input_validation", "tests/test_basic.py::TestBasicFunctionality::test_rate_limiting", "tests/test_basic.py::TestBasicFunctionality::test_security_headers", "tests/test_basic.py::TestBasicFunctionality::test_server_is_running", "tests/test_basic.py::TestBasicFunctionality::test_sql_injection_protection", "tests/test_basic.py::TestBasicFunctionality::test_xss_protection"]