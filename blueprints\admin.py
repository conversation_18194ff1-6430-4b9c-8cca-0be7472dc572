"""
管理员蓝图 - 简化版
包含所有管理员相关的路由
"""
from flask import Blueprint, request, jsonify, render_template, redirect, url_for, session
from functools import wraps
from models import db, Admin, Project, Group, Number, Card, get_beijing_time
import config
import secrets

admin_bp = Blueprint('admin', __name__, url_prefix='/admin')


def login_required(f):
    """登录验证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'admin_id' not in session:
            if request.path.startswith('/admin/api/'):
                return jsonify({'success': False, 'error': '未登录'}), 401
            return redirect(url_for('admin.login'))
        return f(*args, **kwargs)
    return decorated_function


@admin_bp.route('/login', methods=['GET', 'POST'])
def login():
    """管理员登录"""
    if request.method == 'GET':
        return render_template('login.html')

    try:
        # 支持表单提交和JSON提交
        if request.is_json:
            data = request.get_json()
            username = data.get('username', '').strip()
            password = data.get('password', '').strip()
        else:
            username = request.form.get('username', '').strip()
            password = request.form.get('password', '').strip()

        if not username or not password:
            if request.is_json:
                return jsonify({'success': False, 'error': '用户名和密码不能为空'})
            else:
                return render_template('login.html', error='用户名和密码不能为空')

        # 查找管理员
        admin = db.session.execute(
            db.select(Admin).filter_by(username=username)
        ).scalars().first()

        if not admin or not admin.check_password(password):
            if request.is_json:
                return jsonify({'success': False, 'error': '用户名或密码错误'})
            else:
                return render_template('login.html', error='用户名或密码错误')

        # 设置会话
        session['admin_id'] = admin.id
        session['admin_username'] = admin.username
        session.permanent = True

        if request.is_json:
            return jsonify({'success': True, 'message': '登录成功'})
        else:
            return redirect(url_for('admin.dashboard'))

    except Exception as e:
        if request.is_json:
            return jsonify({'success': False, 'error': '登录失败'})
        else:
            return render_template('login.html', error='登录失败')


@admin_bp.route('/logout')
def logout():
    """管理员登出"""
    session.clear()
    return redirect(url_for('admin.login'))


@admin_bp.route('/')
@admin_bp.route('/dashboard')
@login_required
def dashboard():
    """管理后台首页"""
    return render_template('admin.html')


@admin_bp.route('/api/projects', methods=['GET', 'POST'])
@login_required
def projects():
    """项目管理API"""
    if request.method == 'GET':
        try:
            projects = db.session.execute(db.select(Project)).scalars().all()
            project_list = []
            
            for project in projects:
                project_list.append({
                    'id': project.id,
                    'name': project.name,
                    'description': project.description,
                    'created_at': project.created_at.strftime('%Y-%m-%d %H:%M:%S') if project.created_at else '',
                    'groups_count': len(project.groups),
                    'cards_count': len(project.cards)
                })
            
            return jsonify({'success': True, 'data': project_list})
            
        except Exception as e:
            return jsonify({'success': False, 'error': '获取项目列表失败'})
    
    elif request.method == 'POST':
        try:
            data = request.get_json()
            name = data.get('name', '').strip()
            description = data.get('description', '').strip()
            
            if not name:
                return jsonify({'success': False, 'error': '项目名称不能为空'})
            
            # 检查项目名称是否已存在
            existing = db.session.execute(
                db.select(Project).filter_by(name=name)
            ).scalars().first()
            
            if existing:
                return jsonify({'success': False, 'error': '项目名称已存在'})
            
            # 创建新项目
            project = Project(
                name=name,
                description=description,
                created_at=get_beijing_time()
            )
            
            db.session.add(project)
            db.session.commit()
            
            return jsonify({'success': True, 'message': '项目创建成功'})
            
        except Exception as e:
            db.session.rollback()
            return jsonify({'success': False, 'error': '创建项目失败'})


@admin_bp.route('/api/groups', methods=['GET', 'POST'])
@login_required
def groups():
    """号码组管理API"""
    if request.method == 'GET':
        try:
            project_id = request.args.get('project_id')
            if not project_id:
                return jsonify({'success': False, 'error': '请选择项目'})
            
            groups = db.session.execute(
                db.select(Group).filter_by(project_id=project_id)
            ).scalars().all()
            
            group_list = []
            for group in groups:
                total_numbers = len(group.numbers)
                available_numbers = len([n for n in group.numbers if n.status == 'available' and not n.is_disabled])
                used_numbers = len([n for n in group.numbers if n.status == 'used'])
                
                group_list.append({
                    'id': group.id,
                    'name': group.name,
                    'token': group.token,
                    'priority': group.priority,
                    'is_disabled': group.is_disabled,
                    'total_numbers': total_numbers,
                    'available_numbers': available_numbers,
                    'used_numbers': used_numbers
                })
            
            return jsonify({'success': True, 'data': group_list})
            
        except Exception as e:
            return jsonify({'success': False, 'error': '获取号码组列表失败'})
    
    elif request.method == 'POST':
        try:
            data = request.get_json()
            project_id = data.get('project_id')
            name = data.get('name', '').strip()
            token = data.get('token', '').strip()
            priority = data.get('priority', 5)
            
            if not project_id or not name or not token:
                return jsonify({'success': False, 'error': '请填写完整信息'})
            
            # 创建新号码组
            group = Group(
                project_id=project_id,
                name=name,
                token=token,
                priority=priority,
                created_at=get_beijing_time()
            )
            
            db.session.add(group)
            db.session.commit()
            
            return jsonify({'success': True, 'message': '号码组创建成功'})
            
        except Exception as e:
            db.session.rollback()
            return jsonify({'success': False, 'error': '创建号码组失败'})


@admin_bp.route('/api/cards', methods=['GET', 'POST'])
@login_required
def cards():
    """卡密管理API"""
    if request.method == 'GET':
        try:
            project_id = request.args.get('project_id')
            if not project_id:
                return jsonify({'success': False, 'error': '请选择项目'})
            
            cards = db.session.execute(
                db.select(Card).filter_by(project_id=project_id)
            ).scalars().all()
            
            card_list = []
            for card in cards:
                card_list.append({
                    'id': card.id,
                    'code': card.code,
                    'is_used': card.is_used,
                    'create_time': card.create_time.strftime('%Y-%m-%d %H:%M:%S') if card.create_time else '',
                    'use_time': card.use_time.strftime('%Y-%m-%d %H:%M:%S') if card.use_time else '',
                    'number': card.number,
                    'sms_code': card.sms_code
                })
            
            return jsonify({'success': True, 'data': card_list})
            
        except Exception as e:
            return jsonify({'success': False, 'error': '获取卡密列表失败'})
    
    elif request.method == 'POST':
        try:
            data = request.get_json()
            project_id = data.get('project_id')
            count = data.get('count', 1)
            
            if not project_id:
                return jsonify({'success': False, 'error': '请选择项目'})
            
            if count < 1 or count > 1000:
                return jsonify({'success': False, 'error': '生成数量必须在1-1000之间'})
            
            # 生成卡密
            cards = []
            for _ in range(count):
                card_code = secrets.token_urlsafe(12)
                card = Card(
                    code=card_code,
                    project_id=project_id,
                    create_time=get_beijing_time()
                )
                cards.append(card)
                db.session.add(card)
            
            db.session.commit()
            
            return jsonify({'success': True, 'message': f'成功生成{count}个卡密'})
            
        except Exception as e:
            db.session.rollback()
            return jsonify({'success': False, 'error': '生成卡密失败'})


@admin_bp.route('/api/stats')
@login_required
def stats():
    """统计数据API"""
    try:
        # 基础统计
        total_projects = db.session.execute(db.select(db.func.count(Project.id))).scalar()
        total_groups = db.session.execute(db.select(db.func.count(Group.id))).scalar()
        total_cards = db.session.execute(db.select(db.func.count(Card.id))).scalar()
        used_cards = db.session.execute(db.select(db.func.count(Card.id)).filter_by(is_used=True)).scalar()
        
        return jsonify({
            'success': True,
            'data': {
                'total_projects': total_projects,
                'total_groups': total_groups,
                'total_cards': total_cards,
                'used_cards': used_cards,
                'unused_cards': total_cards - used_cards
            }
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': '获取统计数据失败'})
