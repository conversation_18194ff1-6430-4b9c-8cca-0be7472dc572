"""
API蓝图 - 简化版
包含所有API相关的路由
"""
import requests
from flask import Blueprint, request, jsonify
from models import db, Group, Number, get_beijing_time
import config

api_bp = Blueprint('api', __name__, url_prefix='/api')


@api_bp.route('/add_numbers', methods=['POST'])
def add_numbers():
    """添加号码到号码组"""
    try:
        data = request.get_json()
        group_id = data.get('group_id')
        numbers_data = data.get('numbers', [])
        
        if not group_id:
            return jsonify({'success': False, 'error': '请选择号码组'})
        
        if not numbers_data:
            return jsonify({'success': False, 'error': '请提供号码数据'})
        
        # 查找号码组
        group = db.session.execute(
            db.select(Group).filter_by(id=group_id)
        ).scalars().first()
        
        if not group:
            return jsonify({'success': False, 'error': '号码组不存在'})
        
        added_count = 0
        for number_data in numbers_data:
            number = number_data.get('number', '').strip()
            com = number_data.get('com', '').strip()
            
            if not number:
                continue
            
            # 检查号码是否已存在
            existing = db.session.execute(
                db.select(Number).filter_by(number=number, group_id=group_id)
            ).scalars().first()
            
            if existing:
                continue
            
            # 创建新号码
            new_number = Number(
                number=number,
                com=com,
                group_id=group_id,
                status='available',
                created_at=get_beijing_time()
            )
            
            db.session.add(new_number)
            added_count += 1
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'成功添加{added_count}个号码'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': '添加号码失败'})


@api_bp.route('/get_numbers')
def get_numbers():
    """获取号码列表"""
    try:
        group_id = request.args.get('group_id')
        if not group_id:
            return jsonify({'success': False, 'error': '请选择号码组'})
        
        numbers = db.session.execute(
            db.select(Number).filter_by(group_id=group_id)
        ).scalars().all()
        
        number_list = []
        for number in numbers:
            number_list.append({
                'id': number.id,
                'number': number.number,
                'com': number.com,
                'status': number.status,
                'is_disabled': number.is_disabled,
                'created_at': number.created_at.strftime('%Y-%m-%d %H:%M:%S') if number.created_at else '',
                'last_use_time': number.last_use_time.strftime('%Y-%m-%d %H:%M:%S') if number.last_use_time else ''
            })
        
        return jsonify({'success': True, 'data': number_list})
        
    except Exception as e:
        return jsonify({'success': False, 'error': '获取号码列表失败'})


@api_bp.route('/test_sms_api')
def test_sms_api():
    """测试SMS API连接"""
    try:
        test_url = f"{config.SMS_API_HOST}/api/status"
        response = requests.get(test_url, timeout=10)
        
        if response.status_code == 200:
            return jsonify({
                'success': True,
                'message': 'SMS API连接正常',
                'status_code': response.status_code
            })
        else:
            return jsonify({
                'success': False,
                'error': f'SMS API返回状态码: {response.status_code}'
            })
            
    except requests.RequestException as e:
        return jsonify({
            'success': False,
            'error': f'SMS API连接失败: {str(e)}'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': '测试SMS API时发生错误'
        })


@api_bp.route('/cleanup_expired')
def cleanup_expired():
    """清理过期数据"""
    try:
        # 这里可以添加清理过期卡密、号码等逻辑
        # 目前只返回成功消息
        return jsonify({
            'success': True,
            'message': '清理完成'
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': '清理失败'})
