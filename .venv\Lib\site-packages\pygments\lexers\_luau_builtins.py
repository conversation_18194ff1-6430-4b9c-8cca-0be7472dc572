"""
    pygments.lexers._luau_builtins
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

    Includes the builtins for Luau and Roblox.

    :copyright: Copyright 2006-2025 by the Pygments team, see AUTHORS.
    :license: BSD, see LICENSE for details.
"""

LUAU_BUILTINS = {
	'bit32',
	'buffer',
	'coroutine',
	'debug',
	'math',
	'os',
	'string',
	'table',
	'utf8',
}

ROBLOX_BUILTINS = {
	'task',

	'Axes',
	'BrickColor',
	'CatalogSearchParams',
	'CFrame',
	'Color3',
	'ColorSequence',
	'ColorSequenceKeypoint',
	'DateTime',
	'DockWidgetPluginGuiInfo',
	'Faces',
	'FloatCurveKey',
	'Font',
	'Instance',
	'NumberRange',
	'NumberSequence',
	'NumberSequenceKeypoint',
	'OverlapParams',
	'PathWaypoint',
	'PhysicalProperties',
	'Random',
	'Ray',
	'RaycastParams',
	'RaycastResult',
	'RBXScriptConnection',
	'RBXScriptSignal',
	'Rect',
	'Region3',
	'Region3int16',
	'SharedTable',
	'TweenInfo',
	'UDim',
	'UDim2',
	'Vector2',
	'Vector2int16',
	'Vector3',
	'Vector3int16',
}