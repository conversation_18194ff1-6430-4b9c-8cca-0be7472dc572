from datetime import datetime, timezone, timedelta
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash

db = SQLAlchemy()


def get_beijing_time():
    """获取北京时间"""
    beijing_tz = timezone(timedelta(hours=8))
    return datetime.now(beijing_tz)

class Admin(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, nullable=False)
    password_hash = db.Column(db.String(128), nullable=False)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Project(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    sms_title = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)  # 项目描述，将显示在用户前台底部
    is_random_global = db.Column(db.Boolean, default=False)  # 号码组随机取号开关
    code_regex_patterns = db.Column(db.Text, nullable=True)  # 验证码正则表达式，JSON格式存储
    created_at = db.Column(db.DateTime, default=get_beijing_time)
    groups = db.relationship('Group', backref='project', lazy=True, cascade="all, delete-orphan")

class Group(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)  # 号码组名称
    project_id = db.Column(db.Integer, db.ForeignKey('project.id'), nullable=False)
    token = db.Column(db.String(100), nullable=False)
    order = db.Column(db.Integer, default=5)  # 顺序，组用完自动切换，默认改为5
    is_random = db.Column(db.Boolean, default=True)  # 是否随机取号，默认改为True
    is_disabled = db.Column(db.Boolean, default=False)  # 是否禁用，禁用后不会分配号码
    numbers = db.relationship('Number', backref='group', lazy=True, cascade="all, delete-orphan")

class Number(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    group_id = db.Column(db.Integer, db.ForeignKey('group.id'), nullable=False)
    com = db.Column(db.Integer)
    number = db.Column(db.String(20))
    is_used = db.Column(db.Boolean, default=False)
    status = db.Column(db.String(20), default='available')  # available, in_use, used
    last_use_time = db.Column(db.DateTime)  # 最后一次使用时间，用于超时释放
    is_disabled = db.Column(db.Boolean, default=False)  # 是否禁用，禁用状态下的号码不可获取

class Card(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(20), nullable=False)
    project_id = db.Column(db.Integer, db.ForeignKey('project.id'), nullable=False)
    project = db.relationship('Project', backref='cards')
    is_used = db.Column(db.Boolean, default=False)
    create_time = db.Column(db.DateTime, default=get_beijing_time)  # 添加创建时间字段，使用北京时间
    use_time = db.Column(db.DateTime)  # 使用时间（获取验证码的时间）
    sms_code = db.Column(db.String(10))  # 保存收到的验证码
    sms_content = db.Column(db.Text)     # 保存完整的短信内容
    number = db.Column(db.String(20))    # 保存分配的号码
    com = db.Column(db.Integer)          # 保存运营商编号

class UserNumber(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    card_code = db.Column(db.String(32))
    number_id = db.Column(db.Integer, db.ForeignKey('number.id'))
    project_id = db.Column(db.Integer, db.ForeignKey('project.id'))
    created_at = db.Column(db.DateTime, default=get_beijing_time)  # 使用北京时间

class GlobalSettings(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(50), unique=True, nullable=False)
    value = db.Column(db.Text)
    description = db.Column(db.String(200))
    created_at = db.Column(db.DateTime, default=get_beijing_time)
    updated_at = db.Column(db.DateTime, default=get_beijing_time, onupdate=get_beijing_time)

# 新增：每日统计表
class DailyStats(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    project_id = db.Column(db.Integer, db.ForeignKey('project.id'), nullable=False)
    date = db.Column(db.Date, nullable=False)
    cards_used = db.Column(db.Integer, default=0)  # 当日使用的卡密数量
    cards_generated = db.Column(db.Integer, default=0)  # 当日生成的卡密数量
    created_at = db.Column(db.DateTime, default=get_beijing_time)
    updated_at = db.Column(db.DateTime, default=get_beijing_time, onupdate=get_beijing_time)

    # 确保每个项目每天只有一条记录
    __table_args__ = (db.UniqueConstraint('project_id', 'date', name='unique_project_date'),)

# 新增：卡密使用记录表（用于跟踪1小时内使用次数）
class CardUsageRecord(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    card_code = db.Column(db.String(32), nullable=False)
    action_type = db.Column(db.String(20), nullable=False)  # 'get_number' 或 'replace_number'
    ip_address = db.Column(db.String(45))  # 支持IPv6
    created_at = db.Column(db.DateTime, default=get_beijing_time)

    # 添加索引以提高查询性能
    __table_args__ = (
        db.Index('idx_card_code_time', 'card_code', 'created_at'),
        db.Index('idx_ip_time', 'ip_address', 'created_at'),
    )

# 新增：卡密与号码绑定表（用于临时绑定和超时管理）
class CardNumberBinding(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    card_code = db.Column(db.String(32), nullable=False, unique=True)  # 一个卡密同时只能绑定一个号码
    number_id = db.Column(db.Integer, db.ForeignKey('number.id'), nullable=False)
    project_id = db.Column(db.Integer, db.ForeignKey('project.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=get_beijing_time)
    expires_at = db.Column(db.DateTime, nullable=False)  # 绑定过期时间

    # 关联关系
    number = db.relationship('Number', backref='bindings')
    project = db.relationship('Project', backref='card_bindings')

    # 添加索引以提高查询性能
    __table_args__ = (
        db.Index('idx_card_code', 'card_code'),
        db.Index('idx_expires_at', 'expires_at'),
        db.Index('idx_number_id', 'number_id'),
    )
